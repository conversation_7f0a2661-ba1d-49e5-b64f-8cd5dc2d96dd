import com.epoint.core.utils.web.WebUtil;
import com.epoint.core.utils.httpclient.HttpUtil;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import com.epoint.core.utils.container.ContainerFactory;
import com.epoint.gateway.engine.api.IAPIInnerService;
import com.epoint.gateway.engine.api.entity.ApiManageInfo;
import com.epoint.core.utils.string.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epoint.api.manage.util.ApiManageCompatible;

def api(def params,def header){
    HttpServletResponse response = WebUtil.getResponse()

    String url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    // 头信息中删除原有认证信息
    header.remove("Authorization")
    header.remove("authorization")
    // 放入新的认证信息
    header.put("Authorization", "Bearer sk-7e1bea7b50f842adafa76203358944b6")
    // 解析 params 为 JSON 对象
    JSONObject jsonParams = JSON.parseObject(params.toString());
    jsonParams.put("model", "qwen3-32b");
    String modifiedParams = jsonParams.toJSONString();

    boolean isStream = jsonParams.containsKey("stream") ? jsonParams.getBoolean("stream") : false
    
    if (!isStream)
    {
        Map<String, String> result = HttpUtil.doHttp(url, header, modifiedParams, "POST", 3);
        def json = JSON.parseObject(result["result"])
        def content ="";
        if (json?.choices?.getAt(0)?.message) {
            content = json.choices[0].message.content
        }
        if(content=="" || content == null){
            throw new RuntimeException("大模型调用异常："+result)
        }else{
            def result1 = [
                status: [code: "0", error_msg: ""],
                result: content,
                request_time: "",
                request_id: "",
                session_id: ""
            ]
            return JSON.toJSONString(result1);
        }
    }
    else
    {
        InputStream inputStream = HttpUtil.doHttp(url, header, modifiedParams, "POST", HttpUtil.RTN_TYPE_INPUTSTREAM);
        if(inputStream==null){
            throw new RuntimeException("大模型调用异常")
        }
        BufferedReader reader = null;
        OutputStream outputStream = null;
        try {
            reader = new BufferedReader(new InputStreamReader(inputStream))
            String line;
            outputStream = response.getOutputStream();
            while ((line = reader.readLine()) != null) {
                String content;
                def toolcalls;
                
                    // System.out.println(line)
                if (line.startsWith("data: ")) {
                    line = line.substring(6)
                }
                if (line.equals("[DONE]"))
                    continue;
                try {
                    def json = JSON.parseObject(line)
                    if (json?.choices?.getAt(0)?.delta?.content) {
                        content = json.choices[0].delta.content
                    }
                    if (json?.choices?.getAt(0)?.delta?.tool_calls) {
                    // System.out.println(json.choices[0].delta.tool_calls)
                        
                        toolcalls = json.choices[0].delta.tool_calls
                    // System.out.println(toolcalls)
                    }
                } catch (Exception ex) {
                    System.err.println(ex)
                    System.err.println("JSON 解析错误: ${line}")
                }
                def resultPayload = content != null ? content : ""

                def result = [
                    status: [code: "0", error_msg: ""],
                    result: resultPayload,
                    request_time: "",
                    request_id: "",
                    session_id: ""
                ]

                // 输出 JSON
                def jsonOutput = JSON.toJSONString(result)
                ApiManageCompatible.WriteLine(outputStream, jsonOutput)

                // 处理响应报文 TODO
                // outputStream.write((line + "\n").getBytes());
                // outputStream.flush();  // 确保客户端立即收到数据
                // ApiManageCompatible.WriteLine(outputStream, line)
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            return e;
        }
        finally {
            // 确保所有资源都被关闭
            try { if (reader != null) reader.close() } catch (IOException e) { println("关闭reader失败") }
            try { if (inputStream != null) inputStream.close() } catch (IOException e) { println("关闭inputStream失败") }
            try { if (outputStream != null) outputStream.close() } catch (IOException e) { println("关闭outputStream失败") }
        }
    }
}
