

import com.epoint.core.utils.web.WebUtil;
import com.epoint.core.utils.httpclient.HttpUtil;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import com.epoint.core.utils.container.ContainerFactory;
import com.epoint.gateway.engine.api.IAPIInnerService;
import com.epoint.gateway.engine.api.entity.ApiManageInfo;
import com.epoint.core.utils.string.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epoint.api.manage.util.ApiManageCompatible;

def api(def params,def header){
    HttpServletResponse response = WebUtil.getResponse()

    // 使用 DashScope 应用模式 API
    String APP_ID = "ac5ab1e2759d432eb6c1f69493962e6e"  // 替换为实际的应用 ID
    String url = "https://dashscope.aliyuncs.com/api/v1/apps/${APP_ID}/completion";

    // 头信息中删除原有认证信息
    header.remove("Authorization")
    header.remove("authorization")
    // 放入新的认证信息
    header.put("Authorization", "Bearer sk-288a3100dca24957afb251eb095fbb48")
    header.put("Content-Type", "application/json")
    header.put("X-DashScope-SSE", "enable")  // 启用流式响应

    // 解析 params 为 JSON 对象
    JSONObject jsonParams = JSON.parseObject(params.toString());

    // 构建 DashScope 应用模式的请求格式
    JSONObject requestData = new JSONObject();
    JSONObject input = new JSONObject();
    JSONObject parameters = new JSONObject();

    // 从原始参数中提取 prompt 和 biz_params
    String prompt = "";
    JSONObject bizParams = new JSONObject();

    if (jsonParams.containsKey("messages")) {
        // 如果是标准格式，提取最后一条用户消息作为 prompt
        def messages = jsonParams.getJSONArray("messages");
        if (messages != null && messages.size() > 0) {
            def lastMessage = messages.getJSONObject(messages.size() - 1);
            if (lastMessage.containsKey("content")) {
                prompt = lastMessage.getString("content");
            }
        }
    } else if (jsonParams.containsKey("prompt")) {
        prompt = jsonParams.getString("prompt");
    }

    // 提取 biz_params（如果存在）
    if (jsonParams.containsKey("biz_params")) {
        bizParams = jsonParams.getJSONObject("biz_params");
    } else {
        // 设置默认的 biz_params
        bizParams.put("sessionid", "default_session_" + System.currentTimeMillis());
        bizParams.put("areaname", "");
        bizParams.put("istest", 0);
        bizParams.put("areacode", "");
    }

    input.put("prompt", prompt);
    input.put("biz_params", bizParams);

    parameters.put("flow_stream_mode", "message_format");

    requestData.put("input", input);
    requestData.put("parameters", parameters);
    requestData.put("debug", new JSONObject());

    String modifiedParams = requestData.toJSONString();

    // 直接使用流式请求
    {
        InputStream inputStream = HttpUtil.doHttp(url, header, modifiedParams, "POST", HttpUtil.RTN_TYPE_INPUTSTREAM);
        if(inputStream==null){
            throw new RuntimeException("DashScope API调用异常")
        }
        BufferedReader reader = null;
        OutputStream outputStream = null;
        try {
            reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))
            String line;
            outputStream = response.getOutputStream();

            while ((line = reader.readLine()) != null) {
                String content = null;
                boolean isFinished = false;

                // 处理 SSE 格式的数据行
                if (line.startsWith("data:")) {
                    String dataStr = line.substring(5).trim();  // 去掉"data:"前缀并去除空格

                    // 跳过空行和结束标记
                    if (dataStr.isEmpty() || dataStr.equals("[DONE]")) {
                        continue;
                    }

                    try {
                        def json = JSON.parseObject(dataStr)

                        // 解析 DashScope 应用模式的响应格式
                        if (json?.output) {
                            def output = json.output

                            // 检查 workflow_message 中的 content
                            if (output.workflow_message?.message?.content) {
                                content = output.workflow_message.message.content
                            }

                            // 检查是否完成
                            if (output.finish_reason == "stop") {
                                isFinished = true
                            }
                        }

                    } catch (Exception ex) {
                        System.err.println("JSON 解析错误: ${dataStr}")
                        System.err.println(ex)
                        continue;
                    }

                    // 只有当有内容时才输出
                    if (content != null && !content.isEmpty()) {
                        def result = [
                            status: [code: "0", error_msg: ""],
                            result: content,
                            request_time: "",
                            request_id: "",
                            session_id: ""
                        ]

                        // 输出 JSON
                        def jsonOutput = JSON.toJSONString(result)
                        ApiManageCompatible.WriteLine(outputStream, jsonOutput)
                    }

                    // 如果完成，跳出循环
                    if (isFinished) {
                        break;
                    }
                }
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            return e;
        }
        finally {
            // 确保所有资源都被关闭
            try { if (reader != null) reader.close() } catch (IOException e) { println("关闭reader失败") }
            try { if (inputStream != null) inputStream.close() } catch (IOException e) { println("关闭inputStream失败") }
            try { if (outputStream != null) outputStream.close() } catch (IOException e) { println("关闭outputStream失败") }
        }
    }
}
