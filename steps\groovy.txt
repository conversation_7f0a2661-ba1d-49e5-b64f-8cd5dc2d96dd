

import com.epoint.core.utils.web.WebUtil;
import com.epoint.core.utils.httpclient.HttpUtil;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import com.epoint.core.utils.container.ContainerFactory;
import com.epoint.gateway.engine.api.IAPIInnerService;
import com.epoint.gateway.engine.api.entity.ApiManageInfo;
import com.epoint.core.utils.string.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epoint.api.manage.util.ApiManageCompatible;

def api(def params,def header){
    HttpServletResponse response = WebUtil.getResponse()

    // 使用 DashScope 应用模式 API
    String APP_ID = "ac5ab1e2759d432eb6c1f69493962e6e"  // 替换为实际的应用 ID
    String url = "https://dashscope.aliyuncs.com/api/v1/apps/${APP_ID}/completion";

    // 头信息中删除原有认证信息
    header.remove("Authorization")
    header.remove("authorization")
    // 放入新的认证信息
    header.put("Authorization", "Bearer sk-288a3100dca24957afb251eb095fbb48")
    header.put("Content-Type", "application/json")
    header.put("X-DashScope-SSE", "enable")  // 启用流式响应

    // 解析 params 为 JSON 对象
    JSONObject jsonParams = JSON.parseObject(params.toString());

    // 构建 DashScope 应用模式的请求格式
    JSONObject requestData = new JSONObject();
    JSONObject input = new JSONObject();
    JSONObject parameters = new JSONObject();

    // 从原始参数中提取 prompt 和 biz_params
    String prompt = "";
    JSONObject bizParams = new JSONObject();

    if (jsonParams.containsKey("messages")) {
        // 如果是标准格式，提取最后一条用户消息作为 prompt
        def messages = jsonParams.getJSONArray("messages");
        if (messages != null && messages.size() > 0) {
            def lastMessage = messages.getJSONObject(messages.size() - 1);
            if (lastMessage.containsKey("content")) {
                prompt = lastMessage.getString("content");
            }
        }
    } else if (jsonParams.containsKey("prompt")) {
        prompt = jsonParams.getString("prompt");
    }

    // 提取 biz_params（如果存在）
    if (jsonParams.containsKey("biz_params")) {
        bizParams = jsonParams.getJSONObject("biz_params");
    } else {
        // 设置默认的 biz_params
        bizParams.put("sessionid", "default_session_" + System.currentTimeMillis());
        bizParams.put("areaname", "");
        bizParams.put("istest", 0);
        bizParams.put("areacode", "");
    }

    input.put("prompt", prompt);
    input.put("biz_params", bizParams);

    parameters.put("flow_stream_mode", "message_format");

    requestData.put("input", input);
    requestData.put("parameters", parameters);
    requestData.put("debug", new JSONObject());

    String modifiedParams = requestData.toJSONString();

    // 检查是否为流式请求（DashScope 应用模式默认为流式）
    boolean isStream = true;  // DashScope 应用模式主要用于流式响应

    if (!isStream)
    {
        // 非流式请求处理（保留原逻辑但适配新的响应格式）
        Map<String, String> result = HttpUtil.doHttp(url, header, modifiedParams, "POST", 3);
        def json = JSON.parseObject(result["result"])
        def content ="";

        // 适配 DashScope 应用模式的响应格式
        if (json?.output?.workflow_message?.message?.content) {
            content = json.output.workflow_message.message.content
        } else if (json?.choices?.getAt(0)?.message) {
            // 兼容原格式
            content = json.choices[0].message.content
        }

        if(content=="" || content == null){
            throw new RuntimeException("大模型调用异常："+result)
        }else{
            def result1 = [
                status: [code: "0", error_msg: ""],
                result: content,
                request_time: "",
                request_id: "",
                session_id: ""
            ]
            return JSON.toJSONString(result1);
        }
    }
    else
    {
        InputStream inputStream = HttpUtil.doHttp(url, header, modifiedParams, "POST", HttpUtil.RTN_TYPE_INPUTSTREAM);
        if(inputStream==null){
            throw new RuntimeException("大模型调用异常")
        }
        BufferedReader reader = null;
        OutputStream outputStream = null;
        try {
            reader = new BufferedReader(new InputStreamReader(inputStream))
            String line;
            outputStream = response.getOutputStream();
            while ((line = reader.readLine()) != null) {
                String content;
                def toolcalls;
                
                    // System.out.println(line)
                if (line.startsWith("data: ")) {
                    line = line.substring(6)
                }
                if (line.equals("[DONE]"))
                    continue;
                try {
                    def json = JSON.parseObject(line)
                    if (json?.choices?.getAt(0)?.delta?.content) {
                        content = json.choices[0].delta.content
                    }
                    if (json?.choices?.getAt(0)?.delta?.tool_calls) {
                    // System.out.println(json.choices[0].delta.tool_calls)
                        
                        toolcalls = json.choices[0].delta.tool_calls
                    // System.out.println(toolcalls)
                    }
                } catch (Exception ex) {
                    System.err.println(ex)
                    System.err.println("JSON 解析错误: ${line}")
                }
                def resultPayload = content != null ? content : ""

                def result = [
                    status: [code: "0", error_msg: ""],
                    result: resultPayload,
                    request_time: "",
                    request_id: "",
                    session_id: ""
                ]

                // 输出 JSON
                def jsonOutput = JSON.toJSONString(result)
                ApiManageCompatible.WriteLine(outputStream, jsonOutput)

                // 处理响应报文 TODO
                // outputStream.write((line + "\n").getBytes());
                // outputStream.flush();  // 确保客户端立即收到数据
                // ApiManageCompatible.WriteLine(outputStream, line)
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            return e;
        }
        finally {
            // 确保所有资源都被关闭
            try { if (reader != null) reader.close() } catch (IOException e) { println("关闭reader失败") }
            try { if (inputStream != null) inputStream.close() } catch (IOException e) { println("关闭inputStream失败") }
            try { if (outputStream != null) outputStream.close() } catch (IOException e) { println("关闭outputStream失败") }
        }
    }
}
